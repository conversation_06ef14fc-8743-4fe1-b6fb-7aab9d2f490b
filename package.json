{"name": "cluelessly", "version": "1.0.2", "description": "A powerful desktop application that helps developers solve coding problems by analyzing screenshots of code and providing AI-powered solutions.", "main": "./out/main/index.js", "author": "https://github.com/Xeven777", "homepage": "https://cluelessly.netlify.app/", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/openai": "^1.3.22", "@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query": "^5.80.6", "@types/react-syntax-highlighter": "^15.5.13", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "openai": "^4.104.0", "postcss": "^8.5.4", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "screenshot-desktop": "^1.15.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "uuid": "^11.1.0", "zod": "^3.25.55"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.1.0", "@electron-toolkit/tsconfig": "^1.0.1", "@electron/notarize": "^3.0.1", "@types/node": "^22.15.30", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.1", "electron": "36.4.0", "electron-builder": "^26.0.12", "electron-vite": "^3.1.0", "eslint": "^9.28.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3", "vite": "^6.3.5"}, "pnpm": {"onlyBuiltDependencies": ["electron", "esbuild"]}}